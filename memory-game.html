<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Game - Game Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1 class="nav-logo">🎮 Game Dashboard</h1>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="snake-game.html" class="nav-link">Snake</a></li>
                <li><a href="pong-game.html" class="nav-link">Pong</a></li>
                <li><a href="memory-game.html" class="nav-link active">Memory</a></li>
                <li><a href="leaderboard.html" class="nav-link">Leaderboard</a></li>
                <li><a href="profile.html" class="nav-link">Profile</a></li>
                <li><button id="auth-btn" class="auth-btn">Login</button></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="game-container">
            <h1>🧠 Memory Game</h1>
            <p>Click on cards to flip them and find matching pairs!</p>
            
            <div class="game-controls">
                <div class="game-score">Time: <span id="timer">0</span>s</div>
                <div class="game-score">Moves: <span id="moves">0</span></div>
                <div class="game-score">Best Time: <span id="best-time">--</span></div>
                <button id="start-btn" class="game-btn">Start Game</button>
                <button id="reset-btn" class="game-btn">Reset</button>
            </div>
            
            <div id="game-board" class="memory-board"></div>
            
            <div class="game-instructions">
                <h3>How to Play:</h3>
                <ul>
                    <li>Click on cards to flip them over</li>
                    <li>Find matching pairs of cards</li>
                    <li>Match all pairs to win the game</li>
                    <li>Try to complete the game in the shortest time possible!</li>
                    <li>Your score is based on completion time</li>
                </ul>
            </div>
        </div>

        <div class="auth-section" id="auth-section">
            <div class="auth-container">
                <h2>Login to Save Your Score</h2>
                <p>Create an account or login to save your best times and compete on the leaderboard!</p>
                <a href="index.html" class="play-btn">Go to Login</a>
            </div>
        </div>
    </main>

    <style>
        .memory-board {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .memory-card {
            width: 80px;
            height: 80px;
            background: #667eea;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 2rem;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            transform-style: preserve-3d;
        }

        .memory-card:hover {
            transform: scale(1.05);
        }

        .memory-card.flipped {
            background: white;
            color: #333;
            border: 2px solid #667eea;
        }

        .memory-card.matched {
            background: #4ecdc4;
            color: white;
            cursor: default;
        }

        .memory-card.matched:hover {
            transform: none;
        }

        .memory-card:disabled {
            cursor: not-allowed;
        }
    </style>

    <script src="js/supabase-config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/memory.js"></script>
    <script>
        // Initialize the game page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthState();
            loadBestTime();
            initMemoryGame();
        });

        async function loadBestTime() {
            const user = await getCurrentUser();
            if (!user) return;

            try {
                const { data: scores } = await supabase
                    .from('game_scores')
                    .select('score')
                    .eq('user_id', user.id)
                    .eq('game_type', 'memory')
                    .order('score', { ascending: true })
                    .limit(1);

                if (scores && scores.length > 0) {
                    document.getElementById('best-time').textContent = `${scores[0].score}s`;
                }
            } catch (error) {
                console.error('Error loading best time:', error);
            }
        }

        // Override auth state display for game pages
        function showAuthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'none';
            }
            if (authBtn) {
                authBtn.textContent = 'Logout';
                authBtn.onclick = logout;
            }
        }

        function showUnauthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'flex';
            }
            if (authBtn) {
                authBtn.textContent = 'Login';
                authBtn.onclick = () => window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
