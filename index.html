<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1 class="nav-logo">🎮 Game Dashboard</h1>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link active">Home</a></li>
                <li><a href="snake-game.html" class="nav-link">Snake</a></li>
                <li><a href="pong-game.html" class="nav-link">Pong</a></li>
                <li><a href="memory-game.html" class="nav-link">Memory</a></li>
                <li><a href="leaderboard.html" class="nav-link">Leaderboard</a></li>
                <li><a href="profile.html" class="nav-link">Profile</a></li>
                <li><button id="auth-btn" class="auth-btn">Login</button></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="hero-section">
            <h1>Welcome to Game Dashboard</h1>
            <p>Play amazing 2D games and compete with friends!</p>
        </div>

        <div class="auth-section" id="auth-section">
            <div class="auth-container">
                <h2 id="auth-title">Login to Your Account</h2>
                <form id="auth-form">
                    <input type="email" id="email" placeholder="Email" required>
                    <input type="password" id="password" placeholder="Password" required>
                    <button type="submit" id="submit-btn">Login</button>
                </form>
                <p class="auth-switch">
                    <span id="auth-switch-text">Don't have an account?</span>
                    <button id="switch-mode" class="link-btn">Sign Up</button>
                </p>
            </div>
        </div>

        <div class="games-grid" id="games-grid" style="display: none;">
            <div class="game-card">
                <div class="game-icon">🐍</div>
                <h3>Snake Game</h3>
                <p>Classic snake game - eat food and grow longer!</p>
                <a href="snake-game.html" class="play-btn">Play Now</a>
            </div>

            <div class="game-card">
                <div class="game-icon">🏓</div>
                <h3>Pong Game</h3>
                <p>Classic pong game - bounce the ball and score!</p>
                <a href="pong-game.html" class="play-btn">Play Now</a>
            </div>

            <div class="game-card">
                <div class="game-icon">🧠</div>
                <h3>Memory Game</h3>
                <p>Test your memory by matching card pairs!</p>
                <a href="memory-game.html" class="play-btn">Play Now</a>
            </div>
        </div>

        <div class="stats-section" id="stats-section" style="display: none;">
            <h2>Your Stats</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Games Played</h3>
                    <span id="games-played">0</span>
                </div>
                <div class="stat-card">
                    <h3>Best Snake Score</h3>
                    <span id="best-snake">0</span>
                </div>
                <div class="stat-card">
                    <h3>Best Pong Score</h3>
                    <span id="best-pong">0</span>
                </div>
                <div class="stat-card">
                    <h3>Best Memory Time</h3>
                    <span id="best-memory">--</span>
                </div>
            </div>
        </div>
    </main>

    <script src="js/supabase-config.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize the home page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthState();
            loadUserStats();
        });

        async function loadUserStats() {
            const user = await getCurrentUser();
            if (!user) return;

            try {
                const { data: scores } = await supabase
                    .from('game_scores')
                    .select('*')
                    .eq('user_id', user.id);

                if (scores) {
                    const snakeScores = scores.filter(s => s.game_type === 'snake');
                    const pongScores = scores.filter(s => s.game_type === 'pong');
                    const memoryScores = scores.filter(s => s.game_type === 'memory');

                    document.getElementById('games-played').textContent = scores.length;
                    document.getElementById('best-snake').textContent = 
                        snakeScores.length > 0 ? Math.max(...snakeScores.map(s => s.score)) : 0;
                    document.getElementById('best-pong').textContent = 
                        pongScores.length > 0 ? Math.max(...pongScores.map(s => s.score)) : 0;
                    
                    if (memoryScores.length > 0) {
                        const bestTime = Math.min(...memoryScores.map(s => s.score));
                        document.getElementById('best-memory').textContent = `${bestTime}s`;
                    }
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
    </script>
</body>
</html>
