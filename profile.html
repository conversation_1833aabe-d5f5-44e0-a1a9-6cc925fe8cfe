<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Game Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1 class="nav-logo">🎮 Game Dashboard</h1>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="snake-game.html" class="nav-link">Snake</a></li>
                <li><a href="pong-game.html" class="nav-link">Pong</a></li>
                <li><a href="memory-game.html" class="nav-link">Memory</a></li>
                <li><a href="leaderboard.html" class="nav-link">Leaderboard</a></li>
                <li><a href="profile.html" class="nav-link active">Profile</a></li>
                <li><button id="auth-btn" class="auth-btn">Login</button></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="hero-section">
            <h1>👤 User Profile</h1>
            <p>Manage your account and view your gaming statistics</p>
        </div>

        <div class="auth-section" id="auth-section">
            <div class="auth-container">
                <h2>Login to View Profile</h2>
                <p>Create an account or login to manage your profile and view your gaming statistics!</p>
                <a href="index.html" class="play-btn">Go to Login</a>
            </div>
        </div>

        <div id="profile-content" style="display: none;">
            <div class="profile-container">
                <div class="profile-header">
                    <div class="profile-avatar" id="profile-avatar">👤</div>
                    <h2 id="profile-name">Loading...</h2>
                    <p id="profile-email">Loading...</p>
                </div>

                <form class="profile-form" id="profile-form">
                    <h3>Update Profile</h3>
                    <input type="text" id="username" placeholder="Username" required>
                    <input type="text" id="display-name" placeholder="Display Name" required>
                    <button type="submit">Update Profile</button>
                </form>
            </div>

            <div class="stats-section">
                <h2>Your Gaming Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Games Played</h3>
                        <span id="total-games">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>Best Snake Score</h3>
                        <span id="best-snake">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>Best Pong Score</h3>
                        <span id="best-pong">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>Best Memory Time</h3>
                        <span id="best-memory">--</span>
                    </div>
                </div>
            </div>

            <div class="recent-games-section">
                <h2>Recent Games</h2>
                <div id="recent-games-loading" class="loading">Loading recent games...</div>
                <div id="no-recent-games" class="no-scores" style="display: none;">
                    <p>No games played yet! Start playing to see your recent games here.</p>
                </div>
                <table id="recent-games-table" class="leaderboard-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>Game</th>
                            <th>Score</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody id="recent-games-body">
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <style>
        .recent-games-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-top: 2rem;
        }

        .recent-games-section h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }

        .game-icon-cell {
            font-size: 1.5rem;
        }
    </style>

    <script src="js/supabase-config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/profile.js"></script>
    <script>
        // Initialize the profile page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthState();
        });

        // Override auth state display for profile page
        async function showAuthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'none';
            }
            if (document.getElementById('profile-content')) {
                document.getElementById('profile-content').style.display = 'block';
            }
            if (authBtn) {
                authBtn.textContent = 'Logout';
                authBtn.onclick = logout;
            }
            
            // Load profile data
            await loadProfileData();
            await loadUserStats();
            await loadRecentGames();
        }

        function showUnauthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'flex';
            }
            if (document.getElementById('profile-content')) {
                document.getElementById('profile-content').style.display = 'none';
            }
            if (authBtn) {
                authBtn.textContent = 'Login';
                authBtn.onclick = () => window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
