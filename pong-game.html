<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pong Game - Game Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1 class="nav-logo">🎮 Game Dashboard</h1>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="snake-game.html" class="nav-link">Snake</a></li>
                <li><a href="pong-game.html" class="nav-link active">Pong</a></li>
                <li><a href="memory-game.html" class="nav-link">Memory</a></li>
                <li><a href="leaderboard.html" class="nav-link">Leaderboard</a></li>
                <li><a href="profile.html" class="nav-link">Profile</a></li>
                <li><button id="auth-btn" class="auth-btn">Login</button></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="game-container">
            <h1>🏓 Pong Game</h1>
            <p>Use W/S keys or mouse to control your paddle. First to 10 points wins!</p>
            
            <div class="game-controls">
                <div class="game-score">Player: <span id="player-score">0</span></div>
                <div class="game-score">Computer: <span id="computer-score">0</span></div>
                <div class="game-score">High Score: <span id="high-score">0</span></div>
                <button id="start-btn" class="game-btn">Start Game</button>
                <button id="pause-btn" class="game-btn" disabled>Pause</button>
                <button id="reset-btn" class="game-btn">Reset</button>
            </div>
            
            <canvas id="game-canvas" class="game-canvas" width="600" height="400"></canvas>
            
            <div class="game-instructions">
                <h3>How to Play:</h3>
                <ul>
                    <li>Use W/S keys or move your mouse to control the left paddle</li>
                    <li>Hit the ball back and forth with the computer</li>
                    <li>Score points when the ball passes the opponent's paddle</li>
                    <li>First to 10 points wins the game!</li>
                    <li>Your high score is the maximum points you've scored in a single game</li>
                </ul>
            </div>
        </div>

        <div class="auth-section" id="auth-section">
            <div class="auth-container">
                <h2>Login to Save Your Score</h2>
                <p>Create an account or login to save your high scores and compete on the leaderboard!</p>
                <a href="index.html" class="play-btn">Go to Login</a>
            </div>
        </div>
    </main>

    <script src="js/supabase-config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/pong.js"></script>
    <script>
        // Initialize the game page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthState();
            loadHighScore();
            initPongGame();
        });

        async function loadHighScore() {
            const user = await getCurrentUser();
            if (!user) return;

            try {
                const { data: scores } = await supabase
                    .from('game_scores')
                    .select('score')
                    .eq('user_id', user.id)
                    .eq('game_type', 'pong')
                    .order('score', { ascending: false })
                    .limit(1);

                if (scores && scores.length > 0) {
                    document.getElementById('high-score').textContent = scores[0].score;
                }
            } catch (error) {
                console.error('Error loading high score:', error);
            }
        }

        // Override auth state display for game pages
        function showAuthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'none';
            }
            if (authBtn) {
                authBtn.textContent = 'Logout';
                authBtn.onclick = logout;
            }
        }

        function showUnauthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'flex';
            }
            if (authBtn) {
                authBtn.textContent = 'Login';
                authBtn.onclick = () => window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
