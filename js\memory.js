// Memory Game Logic
let gameBoard;
let gameRunning = false;
let gameStartTime;
let gameTimer;
let moves = 0;
let matchedPairs = 0;
let flippedCards = [];
let cards = [];

// Card symbols
const cardSymbols = ['🎮', '🎯', '🎲', '🎪', '🎨', '🎭', '🎪', '🎸'];
const cardPairs = [...cardSymbols, ...cardSymbols]; // Duplicate for pairs

// DOM elements
let timerElement, movesElement, startBtn, resetBtn;

function initMemoryGame() {
    gameBoard = document.getElementById('game-board');
    timerElement = document.getElementById('timer');
    movesElement = document.getElementById('moves');
    startBtn = document.getElementById('start-btn');
    resetBtn = document.getElementById('reset-btn');
    
    // Event listeners
    startBtn.addEventListener('click', startGame);
    resetBtn.addEventListener('click', resetGame);
    
    // Initialize game
    resetGame();
}

function startGame() {
    if (!gameRunning) {
        gameRunning = true;
        gameStartTime = Date.now();
        startBtn.disabled = true;
        
        // Start timer
        gameTimer = setInterval(updateTimer, 100);
        
        // Enable all cards
        cards.forEach(card => {
            card.disabled = false;
        });
    }
}

function resetGame() {
    gameRunning = false;
    clearInterval(gameTimer);
    
    moves = 0;
    matchedPairs = 0;
    flippedCards = [];
    
    startBtn.disabled = false;
    
    updateMoves();
    updateTimer(true);
    createBoard();
}

function createBoard() {
    gameBoard.innerHTML = '';
    cards = [];
    
    // Shuffle cards
    const shuffledCards = shuffleArray([...cardPairs]);
    
    // Create card elements
    shuffledCards.forEach((symbol, index) => {
        const card = document.createElement('button');
        card.className = 'memory-card';
        card.dataset.symbol = symbol;
        card.dataset.index = index;
        card.textContent = '?';
        card.disabled = true;
        
        card.addEventListener('click', () => flipCard(card));
        
        gameBoard.appendChild(card);
        cards.push(card);
    });
}

function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

function flipCard(card) {
    if (!gameRunning || card.classList.contains('flipped') || card.classList.contains('matched')) {
        return;
    }
    
    // Flip the card
    card.classList.add('flipped');
    card.textContent = card.dataset.symbol;
    flippedCards.push(card);
    
    // Check if two cards are flipped
    if (flippedCards.length === 2) {
        moves++;
        updateMoves();
        
        // Disable all cards temporarily
        cards.forEach(c => c.disabled = true);
        
        setTimeout(() => {
            checkMatch();
        }, 1000);
    }
}

function checkMatch() {
    const [card1, card2] = flippedCards;
    
    if (card1.dataset.symbol === card2.dataset.symbol) {
        // Match found
        card1.classList.add('matched');
        card2.classList.add('matched');
        matchedPairs++;
        
        // Check if game is complete
        if (matchedPairs === cardSymbols.length) {
            gameComplete();
        }
    } else {
        // No match - flip cards back
        card1.classList.remove('flipped');
        card2.classList.remove('flipped');
        card1.textContent = '?';
        card2.textContent = '?';
    }
    
    // Clear flipped cards array
    flippedCards = [];
    
    // Re-enable unmatched cards
    cards.forEach(card => {
        if (!card.classList.contains('matched')) {
            card.disabled = false;
        }
    });
}

function updateTimer(reset = false) {
    if (reset) {
        timerElement.textContent = '0';
        return;
    }
    
    if (gameRunning) {
        const elapsed = Math.floor((Date.now() - gameStartTime) / 1000);
        timerElement.textContent = elapsed;
    }
}

function updateMoves() {
    movesElement.textContent = moves;
}

async function gameComplete() {
    gameRunning = false;
    clearInterval(gameTimer);
    
    const finalTime = Math.floor((Date.now() - gameStartTime) / 1000);
    
    // Disable all cards
    cards.forEach(card => card.disabled = true);
    
    startBtn.disabled = false;
    
    // Save score if user is logged in (time is the score - lower is better)
    const saved = await saveGameScore('memory', finalTime);
    
    if (saved) {
        // Update best time display
        const currentBestTime = document.getElementById('best-time').textContent;
        if (currentBestTime === '--' || finalTime < parseInt(currentBestTime)) {
            document.getElementById('best-time').textContent = `${finalTime}s`;
            alert(`New Best Time: ${finalTime} seconds! 🎉\nCompleted in ${moves} moves!`);
        } else {
            alert(`Congratulations! 🎉\nTime: ${finalTime} seconds\nMoves: ${moves}`);
        }
    } else {
        alert(`Congratulations! 🎉\nTime: ${finalTime} seconds\nMoves: ${moves}\nLogin to save your time!`);
    }
}
