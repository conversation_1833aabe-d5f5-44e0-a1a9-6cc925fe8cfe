// Snake Game Logic
let canvas, ctx;
let gameRunning = false;
let gamePaused = false;
let gameLoop;

// Game settings
const GRID_SIZE = 20;
const CANVAS_WIDTH = 400;
const CANVAS_HEIGHT = 400;

// Game state
let snake = [{ x: 200, y: 200 }];
let direction = { x: 0, y: 0 };
let food = { x: 0, y: 0 };
let score = 0;

// DOM elements
let scoreElement, startBtn, pauseBtn, resetBtn;

function initSnakeGame() {
    canvas = document.getElementById('game-canvas');
    ctx = canvas.getContext('2d');
    
    scoreElement = document.getElementById('score');
    startBtn = document.getElementById('start-btn');
    pauseBtn = document.getElementById('pause-btn');
    resetBtn = document.getElementById('reset-btn');
    
    // Event listeners
    startBtn.addEventListener('click', startGame);
    pauseBtn.addEventListener('click', togglePause);
    resetBtn.addEventListener('click', resetGame);
    
    // Keyboard controls
    document.addEventListener('keydown', handleKeyPress);
    
    // Initialize game
    resetGame();
    generateFood();
    draw();
}

function startGame() {
    if (!gameRunning) {
        gameRunning = true;
        gamePaused = false;
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        
        gameLoop = setInterval(() => {
            if (!gamePaused) {
                update();
                draw();
            }
        }, 150);
    }
}

function togglePause() {
    if (gameRunning) {
        gamePaused = !gamePaused;
        pauseBtn.textContent = gamePaused ? 'Resume' : 'Pause';
    }
}

function resetGame() {
    gameRunning = false;
    gamePaused = false;
    clearInterval(gameLoop);
    
    snake = [{ x: 200, y: 200 }];
    direction = { x: 0, y: 0 };
    score = 0;
    
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    pauseBtn.textContent = 'Pause';
    
    updateScore();
    generateFood();
    draw();
}

function handleKeyPress(event) {
    if (!gameRunning || gamePaused) return;
    
    const key = event.key.toLowerCase();
    
    // Prevent reverse direction
    switch (key) {
        case 'arrowup':
        case 'w':
            if (direction.y === 0) {
                direction = { x: 0, y: -GRID_SIZE };
            }
            break;
        case 'arrowdown':
        case 's':
            if (direction.y === 0) {
                direction = { x: 0, y: GRID_SIZE };
            }
            break;
        case 'arrowleft':
        case 'a':
            if (direction.x === 0) {
                direction = { x: -GRID_SIZE, y: 0 };
            }
            break;
        case 'arrowright':
        case 'd':
            if (direction.x === 0) {
                direction = { x: GRID_SIZE, y: 0 };
            }
            break;
    }
}

function update() {
    // Move snake head
    const head = { x: snake[0].x + direction.x, y: snake[0].y + direction.y };
    
    // Check wall collision
    if (head.x < 0 || head.x >= CANVAS_WIDTH || head.y < 0 || head.y >= CANVAS_HEIGHT) {
        gameOver();
        return;
    }
    
    // Check self collision
    for (let segment of snake) {
        if (head.x === segment.x && head.y === segment.y) {
            gameOver();
            return;
        }
    }
    
    snake.unshift(head);
    
    // Check food collision
    if (head.x === food.x && head.y === food.y) {
        score += 10;
        updateScore();
        generateFood();
    } else {
        snake.pop();
    }
}

function generateFood() {
    let validPosition = false;
    
    while (!validPosition) {
        food.x = Math.floor(Math.random() * (CANVAS_WIDTH / GRID_SIZE)) * GRID_SIZE;
        food.y = Math.floor(Math.random() * (CANVAS_HEIGHT / GRID_SIZE)) * GRID_SIZE;
        
        validPosition = true;
        for (let segment of snake) {
            if (food.x === segment.x && food.y === segment.y) {
                validPosition = false;
                break;
            }
        }
    }
}

function draw() {
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    
    // Draw snake
    ctx.fillStyle = '#0f0';
    for (let segment of snake) {
        ctx.fillRect(segment.x, segment.y, GRID_SIZE - 2, GRID_SIZE - 2);
    }
    
    // Draw food
    ctx.fillStyle = '#f00';
    ctx.fillRect(food.x, food.y, GRID_SIZE - 2, GRID_SIZE - 2);
    
    // Draw grid (optional)
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    for (let i = 0; i <= CANVAS_WIDTH; i += GRID_SIZE) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, CANVAS_HEIGHT);
        ctx.stroke();
    }
    for (let i = 0; i <= CANVAS_HEIGHT; i += GRID_SIZE) {
        ctx.beginPath();
        ctx.moveTo(0, i);
        ctx.lineTo(CANVAS_WIDTH, i);
        ctx.stroke();
    }
}

function updateScore() {
    scoreElement.textContent = score;
}

async function gameOver() {
    gameRunning = false;
    gamePaused = false;
    clearInterval(gameLoop);
    
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    pauseBtn.textContent = 'Pause';
    
    // Save score if user is logged in
    const saved = await saveGameScore('snake', score);
    
    if (saved) {
        // Update high score display
        const currentHighScore = parseInt(document.getElementById('high-score').textContent);
        if (score > currentHighScore) {
            document.getElementById('high-score').textContent = score;
            alert(`New High Score: ${score}! 🎉`);
        } else {
            alert(`Game Over! Score: ${score}`);
        }
    } else {
        alert(`Game Over! Score: ${score}\nLogin to save your score!`);
    }
}
