// Supabase configuration
const SUPABASE_URL = 'https://vogmlmjqifwepbygydsx.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvZ21sbWpxaWZ3ZXBieWd5ZHN4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NzcyNjEsImV4cCI6MjA2NDA1MzI2MX0.6aMvkHl1e729VG6cy_hUxPbN4uSzBanJNUKNemi1DMQ';

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Database table schemas (for reference)
/*
-- Users table (handled by Supa<PERSON> Auth)

-- Game scores table
CREATE TABLE game_scores (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    game_type VARCHAR(50) NOT NULL,
    score INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username VARCHAR(50) UNIQUE,
    display_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE game_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for game_scores
CREATE POLICY "Users can view all game scores" ON game_scores FOR SELECT USING (true);
CREATE POLICY "Users can insert their own scores" ON game_scores FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own scores" ON game_scores FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for user_profiles
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can insert their own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
*/

// Initialize database tables if they don't exist
async function initializeDatabase() {
    try {
        // Check if tables exist by trying to query them
        const { data: scoresData, error: scoresError } = await supabase
            .from('game_scores')
            .select('id')
            .limit(1);

        const { data: profilesData, error: profilesError } = await supabase
            .from('user_profiles')
            .select('id')
            .limit(1);

        // If tables don't exist, we'll need to create them via SQL
        if (scoresError && scoresError.code === 'PGRST116') {
            console.log('Database tables need to be created. Please run the SQL commands in the Supabase dashboard.');
        }
    } catch (error) {
        console.error('Error checking database:', error);
    }
}

// Call initialization
initializeDatabase();
