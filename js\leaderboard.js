// Leaderboard functionality
let currentGameType = 'snake';
let currentUser = null;

async function showLeaderboard(gameType) {
    currentGameType = gameType;
    
    // Update active tab
    document.querySelectorAll('.game-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.getElementById(`${gameType}-tab`).classList.add('active');
    
    // Update title
    const titles = {
        snake: 'Snake High Scores',
        pong: 'Pong High Scores',
        memory: 'Memory Best Times'
    };
    document.getElementById('leaderboard-title').textContent = titles[gameType];
    
    // Show loading
    document.getElementById('loading').style.display = 'block';
    document.getElementById('no-scores').style.display = 'none';
    document.getElementById('leaderboard-table').style.display = 'none';
    
    // Load leaderboard data
    await loadLeaderboardData(gameType);
}

async function loadLeaderboardData(gameType) {
    try {
        // Get current user
        currentUser = await getCurrentUser();
        
        // Determine sort order (ascending for memory time, descending for scores)
        const ascending = gameType === 'memory';
        
        // Query leaderboard data with user profiles
        const { data: scores, error } = await supabase
            .from('game_scores')
            .select(`
                score,
                created_at,
                user_profiles!inner(username, display_name)
            `)
            .eq('game_type', gameType)
            .order('score', { ascending })
            .limit(50);
        
        if (error) {
            console.error('Error loading leaderboard:', error);
            showError('Failed to load leaderboard data');
            return;
        }
        
        if (!scores || scores.length === 0) {
            showNoScores();
            return;
        }
        
        // Process and group scores by user (best score per user)
        const userBestScores = new Map();
        
        scores.forEach(score => {
            const userId = score.user_profiles.username;
            const currentBest = userBestScores.get(userId);
            
            if (!currentBest) {
                userBestScores.set(userId, score);
            } else {
                // For memory (time), lower is better; for others, higher is better
                const isBetter = gameType === 'memory' 
                    ? score.score < currentBest.score 
                    : score.score > currentBest.score;
                
                if (isBetter) {
                    userBestScores.set(userId, score);
                }
            }
        });
        
        // Convert to array and sort
        const leaderboardData = Array.from(userBestScores.values())
            .sort((a, b) => {
                return gameType === 'memory' 
                    ? a.score - b.score  // Ascending for time
                    : b.score - a.score; // Descending for score
            });
        
        displayLeaderboard(leaderboardData, gameType);
        
    } catch (error) {
        console.error('Error loading leaderboard:', error);
        showError('Failed to load leaderboard data');
    }
}

function displayLeaderboard(data, gameType) {
    const tbody = document.getElementById('leaderboard-body');
    tbody.innerHTML = '';
    
    data.forEach((entry, index) => {
        const row = document.createElement('tr');
        const rank = index + 1;
        
        // Add special styling for top 3 and current user
        if (rank <= 3) {
            row.classList.add(`rank-${rank}`);
        }
        
        if (currentUser && entry.user_profiles.username === currentUser.email.split('@')[0]) {
            row.classList.add('current-user');
        }
        
        // Format score based on game type
        let scoreDisplay;
        if (gameType === 'memory') {
            scoreDisplay = `${entry.score}s`;
        } else {
            scoreDisplay = entry.score.toString();
        }
        
        // Format date
        const date = new Date(entry.created_at).toLocaleDateString();
        
        row.innerHTML = `
            <td>${getRankDisplay(rank)}</td>
            <td>${entry.user_profiles.display_name || entry.user_profiles.username}</td>
            <td>${scoreDisplay}</td>
            <td>${date}</td>
        `;
        
        tbody.appendChild(row);
    });
    
    // Hide loading and show table
    document.getElementById('loading').style.display = 'none';
    document.getElementById('leaderboard-table').style.display = 'table';
}

function getRankDisplay(rank) {
    switch (rank) {
        case 1:
            return '🥇 1st';
        case 2:
            return '🥈 2nd';
        case 3:
            return '🥉 3rd';
        default:
            return `${rank}th`;
    }
}

function showNoScores() {
    document.getElementById('loading').style.display = 'none';
    document.getElementById('no-scores').style.display = 'block';
    document.getElementById('leaderboard-table').style.display = 'none';
}

function showError(message) {
    document.getElementById('loading').style.display = 'none';
    document.getElementById('loading').textContent = message;
    document.getElementById('loading').style.display = 'block';
    document.getElementById('no-scores').style.display = 'none';
    document.getElementById('leaderboard-table').style.display = 'none';
}

// Refresh leaderboard every 30 seconds
setInterval(() => {
    if (currentUser && document.getElementById('leaderboard-content').style.display !== 'none') {
        loadLeaderboardData(currentGameType);
    }
}, 30000);
