// Pong Game Logic
let canvas, ctx;
let gameRunning = false;
let gamePaused = false;
let gameLoop;

// Game settings
const CANVAS_WIDTH = 600;
const CANVAS_HEIGHT = 400;
const PADDLE_WIDTH = 10;
const PADDLE_HEIGHT = 80;
const BALL_SIZE = 10;

// Game objects
let playerPaddle = {
    x: 10,
    y: CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2,
    width: PADDLE_WIDTH,
    height: PADDLE_HEIGHT,
    speed: 5
};

let computerPaddle = {
    x: CANVAS_WIDTH - 20,
    y: CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2,
    width: PADDLE_WIDTH,
    height: PADDLE_HEIGHT,
    speed: 3
};

let ball = {
    x: CANVAS_WIDTH / 2,
    y: CANVAS_HEIGHT / 2,
    width: BALL_SIZE,
    height: BALL_SIZE,
    speedX: 4,
    speedY: 3
};

// Game state
let playerScore = 0;
let computerScore = 0;
let keys = {};
let mouseY = 0;

// DOM elements
let playerScoreElement, computerScoreElement, startBtn, pauseBtn, resetBtn;

function initPongGame() {
    canvas = document.getElementById('game-canvas');
    ctx = canvas.getContext('2d');
    
    playerScoreElement = document.getElementById('player-score');
    computerScoreElement = document.getElementById('computer-score');
    startBtn = document.getElementById('start-btn');
    pauseBtn = document.getElementById('pause-btn');
    resetBtn = document.getElementById('reset-btn');
    
    // Event listeners
    startBtn.addEventListener('click', startGame);
    pauseBtn.addEventListener('click', togglePause);
    resetBtn.addEventListener('click', resetGame);
    
    // Controls
    document.addEventListener('keydown', (e) => keys[e.key.toLowerCase()] = true);
    document.addEventListener('keyup', (e) => keys[e.key.toLowerCase()] = false);
    canvas.addEventListener('mousemove', (e) => {
        const rect = canvas.getBoundingClientRect();
        mouseY = e.clientY - rect.top;
    });
    
    // Initialize game
    resetGame();
    draw();
}

function startGame() {
    if (!gameRunning) {
        gameRunning = true;
        gamePaused = false;
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        
        gameLoop = setInterval(() => {
            if (!gamePaused) {
                update();
                draw();
            }
        }, 16); // ~60 FPS
    }
}

function togglePause() {
    if (gameRunning) {
        gamePaused = !gamePaused;
        pauseBtn.textContent = gamePaused ? 'Resume' : 'Pause';
    }
}

function resetGame() {
    gameRunning = false;
    gamePaused = false;
    clearInterval(gameLoop);
    
    playerScore = 0;
    computerScore = 0;
    
    // Reset positions
    playerPaddle.y = CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2;
    computerPaddle.y = CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2;
    resetBall();
    
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    pauseBtn.textContent = 'Pause';
    
    updateScore();
    draw();
}

function resetBall() {
    ball.x = CANVAS_WIDTH / 2;
    ball.y = CANVAS_HEIGHT / 2;
    ball.speedX = (Math.random() > 0.5 ? 1 : -1) * 4;
    ball.speedY = (Math.random() - 0.5) * 6;
}

function update() {
    // Player paddle controls (keyboard)
    if (keys['w'] && playerPaddle.y > 0) {
        playerPaddle.y -= playerPaddle.speed;
    }
    if (keys['s'] && playerPaddle.y < CANVAS_HEIGHT - PADDLE_HEIGHT) {
        playerPaddle.y += playerPaddle.speed;
    }
    
    // Player paddle controls (mouse)
    if (mouseY > 0) {
        const targetY = mouseY - PADDLE_HEIGHT / 2;
        if (targetY >= 0 && targetY <= CANVAS_HEIGHT - PADDLE_HEIGHT) {
            playerPaddle.y = targetY;
        }
    }
    
    // Computer paddle AI
    const ballCenterY = ball.y + ball.height / 2;
    const paddleCenterY = computerPaddle.y + computerPaddle.height / 2;
    
    if (ballCenterY < paddleCenterY - 10) {
        computerPaddle.y -= computerPaddle.speed;
    } else if (ballCenterY > paddleCenterY + 10) {
        computerPaddle.y += computerPaddle.speed;
    }
    
    // Keep computer paddle in bounds
    if (computerPaddle.y < 0) computerPaddle.y = 0;
    if (computerPaddle.y > CANVAS_HEIGHT - PADDLE_HEIGHT) {
        computerPaddle.y = CANVAS_HEIGHT - PADDLE_HEIGHT;
    }
    
    // Ball movement
    ball.x += ball.speedX;
    ball.y += ball.speedY;
    
    // Ball collision with top/bottom walls
    if (ball.y <= 0 || ball.y >= CANVAS_HEIGHT - ball.height) {
        ball.speedY = -ball.speedY;
    }
    
    // Ball collision with paddles
    if (ballCollidesPaddle(ball, playerPaddle) || ballCollidesPaddle(ball, computerPaddle)) {
        ball.speedX = -ball.speedX;
        // Add some randomness to the angle
        ball.speedY += (Math.random() - 0.5) * 2;
        // Increase speed slightly
        ball.speedX *= 1.05;
        ball.speedY *= 1.05;
    }
    
    // Scoring
    if (ball.x < 0) {
        computerScore++;
        updateScore();
        resetBall();
        checkGameEnd();
    } else if (ball.x > CANVAS_WIDTH) {
        playerScore++;
        updateScore();
        resetBall();
        checkGameEnd();
    }
}

function ballCollidesPaddle(ball, paddle) {
    return ball.x < paddle.x + paddle.width &&
           ball.x + ball.width > paddle.x &&
           ball.y < paddle.y + paddle.height &&
           ball.y + ball.height > paddle.y;
}

function draw() {
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    
    // Draw center line
    ctx.strokeStyle = '#fff';
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(CANVAS_WIDTH / 2, 0);
    ctx.lineTo(CANVAS_WIDTH / 2, CANVAS_HEIGHT);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // Draw paddles
    ctx.fillStyle = '#fff';
    ctx.fillRect(playerPaddle.x, playerPaddle.y, playerPaddle.width, playerPaddle.height);
    ctx.fillRect(computerPaddle.x, computerPaddle.y, computerPaddle.width, computerPaddle.height);
    
    // Draw ball
    ctx.fillRect(ball.x, ball.y, ball.width, ball.height);
}

function updateScore() {
    playerScoreElement.textContent = playerScore;
    computerScoreElement.textContent = computerScore;
}

async function checkGameEnd() {
    if (playerScore >= 10 || computerScore >= 10) {
        gameRunning = false;
        gamePaused = false;
        clearInterval(gameLoop);
        
        startBtn.disabled = false;
        pauseBtn.disabled = true;
        pauseBtn.textContent = 'Pause';
        
        const winner = playerScore >= 10 ? 'Player' : 'Computer';
        const finalScore = Math.max(playerScore, computerScore);
        
        // Save score if user is logged in (only save player wins)
        if (winner === 'Player') {
            const saved = await saveGameScore('pong', playerScore);
            
            if (saved) {
                // Update high score display
                const currentHighScore = parseInt(document.getElementById('high-score').textContent);
                if (playerScore > currentHighScore) {
                    document.getElementById('high-score').textContent = playerScore;
                    alert(`New High Score: ${playerScore}! 🎉\nYou won ${playerScore}-${computerScore}!`);
                } else {
                    alert(`You Won! Final Score: ${playerScore}-${computerScore}`);
                }
            } else {
                alert(`You Won! Final Score: ${playerScore}-${computerScore}\nLogin to save your score!`);
            }
        } else {
            alert(`Computer Won! Final Score: ${computerScore}-${playerScore}\nTry again!`);
        }
    }
}
