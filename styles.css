/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-logo {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.auth-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.auth-btn:hover {
    background: #ff5252;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.hero-section {
    text-align: center;
    color: white;
    margin-bottom: 3rem;
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Auth Section */
.auth-section {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.auth-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 400px;
}

.auth-container h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
}

.auth-container input {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.auth-container button[type="submit"] {
    width: 100%;
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.auth-container button[type="submit"]:hover {
    background: #5a6fd8;
}

.auth-switch {
    text-align: center;
    margin-top: 1rem;
}

.link-btn {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    text-decoration: underline;
}

/* Games Grid */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.game-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
    transition: transform 0.3s;
}

.game-card:hover {
    transform: translateY(-5px);
}

.game-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.game-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.game-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.play-btn {
    background: #4ecdc4;
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    display: inline-block;
    transition: background-color 0.3s;
}

.play-btn:hover {
    background: #45b7aa;
}

/* Stats Section */
.stats-section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stats-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.stat-card h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-card span {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

/* Game Canvas */
.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
}

.game-canvas {
    border: 2px solid #333;
    background: #000;
}

.game-controls {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
}

.game-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.game-btn:hover {
    background: #5a6fd8;
}

.game-score {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-section h1 {
        font-size: 2rem;
    }

    .main-content {
        padding: 1rem;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Leaderboard Styles */
.leaderboard-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.leaderboard-table th,
.leaderboard-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.leaderboard-table th {
    background: #667eea;
    color: white;
    font-weight: bold;
}

.leaderboard-table tr:hover {
    background: #f8f9fa;
}

.rank-1 { color: #ffd700; font-weight: bold; }
.rank-2 { color: #c0c0c0; font-weight: bold; }
.rank-3 { color: #cd7f32; font-weight: bold; }

/* Profile Styles */
.profile-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    margin: 0 auto;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    margin: 0 auto 1rem;
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.profile-form input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.profile-form button {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.profile-form button:hover {
    background: #5a6fd8;
}
