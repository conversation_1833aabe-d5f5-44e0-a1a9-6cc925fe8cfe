// Profile management functionality
let userProfile = null;

async function loadProfileData() {
    const user = await getCurrentUser();
    if (!user) return;

    try {
        // Load user profile
        const { data: profile, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', user.id)
            .single();

        if (error && error.code !== 'PGRST116') {
            console.error('Error loading profile:', error);
            return;
        }

        userProfile = profile;

        // Update profile display
        document.getElementById('profile-name').textContent = 
            profile?.display_name || user.email.split('@')[0];
        document.getElementById('profile-email').textContent = user.email;
        
        // Set avatar initial
        const initial = (profile?.display_name || user.email).charAt(0).toUpperCase();
        document.getElementById('profile-avatar').textContent = initial;

        // Populate form
        document.getElementById('username').value = profile?.username || user.email.split('@')[0];
        document.getElementById('display-name').value = profile?.display_name || user.email.split('@')[0];

    } catch (error) {
        console.error('Error loading profile data:', error);
    }
}

async function loadUserStats() {
    const user = await getCurrentUser();
    if (!user) return;

    try {
        const { data: scores } = await supabase
            .from('game_scores')
            .select('*')
            .eq('user_id', user.id);

        if (scores) {
            const snakeScores = scores.filter(s => s.game_type === 'snake');
            const pongScores = scores.filter(s => s.game_type === 'pong');
            const memoryScores = scores.filter(s => s.game_type === 'memory');

            document.getElementById('total-games').textContent = scores.length;
            document.getElementById('best-snake').textContent = 
                snakeScores.length > 0 ? Math.max(...snakeScores.map(s => s.score)) : 0;
            document.getElementById('best-pong').textContent = 
                pongScores.length > 0 ? Math.max(...pongScores.map(s => s.score)) : 0;
            
            if (memoryScores.length > 0) {
                const bestTime = Math.min(...memoryScores.map(s => s.score));
                document.getElementById('best-memory').textContent = `${bestTime}s`;
            }
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

async function loadRecentGames() {
    const user = await getCurrentUser();
    if (!user) return;

    try {
        const { data: scores, error } = await supabase
            .from('game_scores')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(10);

        if (error) {
            console.error('Error loading recent games:', error);
            showRecentGamesError('Failed to load recent games');
            return;
        }

        if (!scores || scores.length === 0) {
            showNoRecentGames();
            return;
        }

        displayRecentGames(scores);

    } catch (error) {
        console.error('Error loading recent games:', error);
        showRecentGamesError('Failed to load recent games');
    }
}

function displayRecentGames(scores) {
    const tbody = document.getElementById('recent-games-body');
    tbody.innerHTML = '';

    const gameIcons = {
        snake: '🐍',
        pong: '🏓',
        memory: '🧠'
    };

    scores.forEach(score => {
        const row = document.createElement('tr');
        
        // Format score based on game type
        let scoreDisplay;
        if (score.game_type === 'memory') {
            scoreDisplay = `${score.score}s`;
        } else {
            scoreDisplay = score.score.toString();
        }

        // Format date
        const date = new Date(score.created_at).toLocaleDateString();

        row.innerHTML = `
            <td>
                <span class="game-icon-cell">${gameIcons[score.game_type]}</span>
                ${score.game_type.charAt(0).toUpperCase() + score.game_type.slice(1)}
            </td>
            <td>${scoreDisplay}</td>
            <td>${date}</td>
        `;

        tbody.appendChild(row);
    });

    // Hide loading and show table
    document.getElementById('recent-games-loading').style.display = 'none';
    document.getElementById('recent-games-table').style.display = 'table';
}

function showNoRecentGames() {
    document.getElementById('recent-games-loading').style.display = 'none';
    document.getElementById('no-recent-games').style.display = 'block';
    document.getElementById('recent-games-table').style.display = 'none';
}

function showRecentGamesError(message) {
    document.getElementById('recent-games-loading').textContent = message;
    document.getElementById('no-recent-games').style.display = 'none';
    document.getElementById('recent-games-table').style.display = 'none';
}

// Handle profile form submission
document.getElementById('profile-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const user = await getCurrentUser();
    if (!user) return;

    const username = document.getElementById('username').value.trim();
    const displayName = document.getElementById('display-name').value.trim();

    if (!username || !displayName) {
        alert('Please fill in all fields');
        return;
    }

    try {
        const profileData = {
            id: user.id,
            username: username,
            display_name: displayName,
            updated_at: new Date().toISOString()
        };

        let result;
        if (userProfile) {
            // Update existing profile
            result = await supabase
                .from('user_profiles')
                .update(profileData)
                .eq('id', user.id);
        } else {
            // Create new profile
            profileData.created_at = new Date().toISOString();
            result = await supabase
                .from('user_profiles')
                .insert([profileData]);
        }

        if (result.error) {
            console.error('Error updating profile:', result.error);
            alert('Failed to update profile: ' + result.error.message);
            return;
        }

        alert('Profile updated successfully!');
        
        // Reload profile data
        await loadProfileData();

    } catch (error) {
        console.error('Error updating profile:', error);
        alert('Failed to update profile. Please try again.');
    }
});
