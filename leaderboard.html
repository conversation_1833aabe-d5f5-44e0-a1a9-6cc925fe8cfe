<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaderboard - Game Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1 class="nav-logo">🎮 Game Dashboard</h1>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="snake-game.html" class="nav-link">Snake</a></li>
                <li><a href="pong-game.html" class="nav-link">Pong</a></li>
                <li><a href="memory-game.html" class="nav-link">Memory</a></li>
                <li><a href="leaderboard.html" class="nav-link active">Leaderboard</a></li>
                <li><a href="profile.html" class="nav-link">Profile</a></li>
                <li><button id="auth-btn" class="auth-btn">Login</button></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="hero-section">
            <h1>🏆 Leaderboard</h1>
            <p>See how you rank against other players!</p>
        </div>

        <div class="auth-section" id="auth-section">
            <div class="auth-container">
                <h2>Login to View Leaderboard</h2>
                <p>Create an account or login to see the leaderboard and compete with other players!</p>
                <a href="index.html" class="play-btn">Go to Login</a>
            </div>
        </div>

        <div id="leaderboard-content" style="display: none;">
            <div class="game-selector">
                <button id="snake-tab" class="game-tab active" onclick="showLeaderboard('snake')">🐍 Snake</button>
                <button id="pong-tab" class="game-tab" onclick="showLeaderboard('pong')">🏓 Pong</button>
                <button id="memory-tab" class="game-tab" onclick="showLeaderboard('memory')">🧠 Memory</button>
            </div>

            <div class="leaderboard-section">
                <h2 id="leaderboard-title">Snake High Scores</h2>
                <div id="loading" class="loading">Loading leaderboard...</div>
                <div id="no-scores" class="no-scores" style="display: none;">
                    <p>No scores yet! Be the first to play and set a record!</p>
                </div>
                <table id="leaderboard-table" class="leaderboard-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Player</th>
                            <th>Score</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody id="leaderboard-body">
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <style>
        .game-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .game-tab {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .game-tab:hover {
            background: #f8f9fa;
        }

        .game-tab.active {
            background: #667eea;
            color: white;
        }

        .leaderboard-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .leaderboard-section h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }

        .loading, .no-scores {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-size: 1.1rem;
        }

        .leaderboard-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .leaderboard-table th {
            background: #667eea;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: bold;
        }

        .leaderboard-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
        }

        .leaderboard-table tr:hover {
            background: #f8f9fa;
        }

        .rank-1 { 
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            font-weight: bold;
        }

        .rank-2 { 
            background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
            font-weight: bold;
        }

        .rank-3 { 
            background: linear-gradient(45deg, #cd7f32, #daa520);
            font-weight: bold;
        }

        .current-user {
            background: #e3f2fd;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .game-selector {
                flex-direction: column;
                align-items: center;
            }

            .leaderboard-table {
                font-size: 0.9rem;
            }

            .leaderboard-table th,
            .leaderboard-table td {
                padding: 0.5rem;
            }
        }
    </style>

    <script src="js/supabase-config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/leaderboard.js"></script>
    <script>
        // Initialize the leaderboard page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthState();
            showLeaderboard('snake');
        });

        // Override auth state display for leaderboard page
        function showAuthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'none';
            }
            if (document.getElementById('leaderboard-content')) {
                document.getElementById('leaderboard-content').style.display = 'block';
            }
            if (authBtn) {
                authBtn.textContent = 'Logout';
                authBtn.onclick = logout;
            }
        }

        function showUnauthenticatedState() {
            if (document.getElementById('auth-section')) {
                document.getElementById('auth-section').style.display = 'flex';
            }
            if (document.getElementById('leaderboard-content')) {
                document.getElementById('leaderboard-content').style.display = 'none';
            }
            if (authBtn) {
                authBtn.textContent = 'Login';
                authBtn.onclick = () => window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
