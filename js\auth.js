// Authentication functions
let currentUser = null;
let isLoginMode = true;

// DOM elements
const authSection = document.getElementById('auth-section');
const gamesGrid = document.getElementById('games-grid');
const statsSection = document.getElementById('stats-section');
const authBtn = document.getElementById('auth-btn');
const authForm = document.getElementById('auth-form');
const authTitle = document.getElementById('auth-title');
const submitBtn = document.getElementById('submit-btn');
const switchModeBtn = document.getElementById('switch-mode');
const authSwitchText = document.getElementById('auth-switch-text');

// Initialize auth state
async function checkAuthState() {
    try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
            currentUser = user;
            showAuthenticatedState();
        } else {
            showUnauthenticatedState();
        }
    } catch (error) {
        console.error('Error checking auth state:', error);
        showUnauthenticatedState();
    }
}

// Show authenticated state
function showAuthenticatedState() {
    if (authSection) authSection.style.display = 'none';
    if (gamesGrid) gamesGrid.style.display = 'grid';
    if (statsSection) statsSection.style.display = 'block';
    if (authBtn) {
        authBtn.textContent = 'Logout';
        authBtn.onclick = logout;
    }
}

// Show unauthenticated state
function showUnauthenticatedState() {
    if (authSection) authSection.style.display = 'flex';
    if (gamesGrid) gamesGrid.style.display = 'none';
    if (statsSection) statsSection.style.display = 'none';
    if (authBtn) {
        authBtn.textContent = 'Login';
        authBtn.onclick = () => window.location.href = 'index.html';
    }
}

// Handle form submission
if (authForm) {
    authForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        if (isLoginMode) {
            await login(email, password);
        } else {
            await signup(email, password);
        }
    });
}

// Switch between login and signup
if (switchModeBtn) {
    switchModeBtn.addEventListener('click', () => {
        isLoginMode = !isLoginMode;
        
        if (isLoginMode) {
            authTitle.textContent = 'Login to Your Account';
            submitBtn.textContent = 'Login';
            authSwitchText.textContent = "Don't have an account?";
            switchModeBtn.textContent = 'Sign Up';
        } else {
            authTitle.textContent = 'Create New Account';
            submitBtn.textContent = 'Sign Up';
            authSwitchText.textContent = 'Already have an account?';
            switchModeBtn.textContent = 'Login';
        }
    });
}

// Login function
async function login(email, password) {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });
        
        if (error) {
            alert('Login failed: ' + error.message);
            return;
        }
        
        currentUser = data.user;
        showAuthenticatedState();
        
        // Reload page to update stats
        if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
            window.location.reload();
        }
        
    } catch (error) {
        console.error('Login error:', error);
        alert('Login failed. Please try again.');
    }
}

// Signup function
async function signup(email, password) {
    try {
        const { data, error } = await supabase.auth.signUp({
            email: email,
            password: password
        });
        
        if (error) {
            alert('Signup failed: ' + error.message);
            return;
        }
        
        if (data.user) {
            // Create user profile
            await createUserProfile(data.user);
            alert('Account created successfully! Please check your email to verify your account.');
        }
        
    } catch (error) {
        console.error('Signup error:', error);
        alert('Signup failed. Please try again.');
    }
}

// Create user profile
async function createUserProfile(user) {
    try {
        const { error } = await supabase
            .from('user_profiles')
            .insert([
                {
                    id: user.id,
                    username: user.email.split('@')[0],
                    display_name: user.email.split('@')[0],
                    created_at: new Date().toISOString()
                }
            ]);
        
        if (error) {
            console.error('Error creating profile:', error);
        }
    } catch (error) {
        console.error('Error creating user profile:', error);
    }
}

// Logout function
async function logout() {
    try {
        const { error } = await supabase.auth.signOut();
        
        if (error) {
            console.error('Logout error:', error);
            return;
        }
        
        currentUser = null;
        showUnauthenticatedState();
        
        // Redirect to home page
        window.location.href = 'index.html';
        
    } catch (error) {
        console.error('Logout error:', error);
    }
}

// Get current user
async function getCurrentUser() {
    if (currentUser) return currentUser;
    
    try {
        const { data: { user } } = await supabase.auth.getUser();
        currentUser = user;
        return user;
    } catch (error) {
        console.error('Error getting current user:', error);
        return null;
    }
}

// Save game score
async function saveGameScore(gameType, score) {
    const user = await getCurrentUser();
    if (!user) {
        alert('Please login to save your score!');
        return false;
    }
    
    try {
        const { error } = await supabase
            .from('game_scores')
            .insert([
                {
                    user_id: user.id,
                    game_type: gameType,
                    score: score,
                    created_at: new Date().toISOString()
                }
            ]);
        
        if (error) {
            console.error('Error saving score:', error);
            return false;
        }
        
        return true;
    } catch (error) {
        console.error('Error saving game score:', error);
        return false;
    }
}

// Listen for auth state changes
supabase.auth.onAuthStateChange((event, session) => {
    if (event === 'SIGNED_IN') {
        currentUser = session.user;
        showAuthenticatedState();
    } else if (event === 'SIGNED_OUT') {
        currentUser = null;
        showUnauthenticatedState();
    }
});
